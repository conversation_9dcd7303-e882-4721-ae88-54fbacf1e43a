apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-record-service-depl
  labels:
    app: scube-record-service
spec:
  selector:
    matchLabels:
      app: scube-record-service
  template:
    metadata:
      labels:
        app: scube-record-service
    spec:
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-record-service
          image: service_record
          ports:
            - containerPort: 9011
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-record-service-srv.backend.svc.cluster.local
              path: /api/record/actuator/health
              port: 9011
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9011
            initialDelaySeconds: 30
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: <PERSON><PERSON><PERSON><PERSON><PERSON>K_SWAGGER_RECORD_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: keycloak-swagger-record-client-id
                  name: keycloak-swagger-record-client-id
            - name: DATABASE_HOST
              valueFrom:
                secretKeyRef:
                  key: database-host
                  name: database-host
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: spring-datasource-username
                  name: spring-datasource-username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: spring-datasource-password
                  name: spring-datasource-password
            - name: SPRING_RABBITMQ_HOST
              valueFrom:
                secretKeyRef:
                  key: spring-rabbitmq-host
                  name: spring-rabbitmq-host
            - name: KEYCLOAK_URL
              valueFrom:
                secretKeyRef:
                  key: keycloak-url
                  name: keycloak-url
            - name: KEYCLOAK_PUBLIC_URL
              valueFrom:
                secretKeyRef:
                  key: keycloak-public-url
                  name: keycloak-public-url
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: keycloak-admin-client-id
                  name: keycloak-admin-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: keycloak-admin-client-secret
                  name: keycloak-admin-client-secret
            - name: CLIENT_LIB_HOST
              valueFrom:
                secretKeyRef:
                  key: client-lib-host
                  name: client-lib-host
---
apiVersion: v1
kind: Service
metadata:
  name: scube-record-service-srv
spec:
  selector:
    app: scube-record-service
  ports:
    - name: scube-record-service
      protocol: TCP
      port: 9011
      targetPort: 9011