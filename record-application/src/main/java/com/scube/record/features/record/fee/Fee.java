package com.scube.record.features.record.fee;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * Fee DTO for Record Service calculation
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Fee implements Serializable {
    private String key;
    private String feeName;
    private BigDecimal amount;
    private FeeType operation;
    private Map<String, Object> properties;

    public enum FeeType {
        FLAT,
        PERCENTAGE,
        FUNCTION
    }
}
