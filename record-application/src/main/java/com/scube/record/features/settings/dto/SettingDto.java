package com.scube.record.features.settings.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettingDto {

    private Long settingId;
    private String settingKey;
    private String settingValue;
    private String settingType;
    private String category;
    private String description;
    private Boolean isPublic;
    private Boolean isEncrypted;
    private String realm;
    private Map<String, Object> metadata;

    // Audit fields
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private Boolean isDeleted;
}