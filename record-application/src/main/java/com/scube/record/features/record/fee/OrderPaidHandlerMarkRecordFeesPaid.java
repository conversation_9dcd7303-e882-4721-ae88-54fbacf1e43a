package com.scube.record.features.record.fee;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.record.features.entity_fee.service.EntityFeeService;
import com.scube.record.features.record.service.RecordService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * Listens to OrderPaidEvent from Calculation/Payment Service
 * Marks record fees as paid when payment is completed
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderPaidHandlerMarkRecordFeesPaid extends FanoutListener<OrderPaidHandlerMarkRecordFeesPaid.OrderPaidEvent> {
    private final RecordService recordService;
    private final EntityFeeService entityFeeService;

    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void consume(OrderPaidEvent event) {
        if (ObjectUtils.isEmpty(event.orderInvoice().getItems())) {
            log.warn("OrderPaidEvent received with no items");
            throw new FeeInvoiceException();
        }

        log.info("Processing OrderPaidEvent for {} items", event.orderInvoice().getItems().size());

        // Update entity fees as paid
        entityFeeService.updateEntityFeeAsPaid(event.orderInvoice());

        // Process each record item
        var recordItems = event.orderInvoice().getItems().stream()
                .filter(i -> i.getItemType().equalsIgnoreCase("record"))
                .toList();

        log.info("Found {} record items to process", recordItems.size());

        for (var recordItem : recordItems) {
            processRecord(recordItem);
        }
    }

    private void processRecord(OrderInvoiceItem item) {
        log.info("Processing record payment for itemId: {}", item.getItemId());
        
        // TODO: Implement record-specific payment processing logic
        // This is where you would:
        // 1. Update record status after payment
        // 2. Trigger any post-payment workflows
        // 3. Generate documents/certificates if needed
        
        // For now, just log
        log.info("Record {} payment processed successfully", item.getItemId());
    }

    public record OrderPaidEvent(OrderInvoiceResponse orderInvoice) implements IRabbitFanoutSubscriber {
    }

    public static class FeeInvoiceException extends RuntimeException {
        public FeeInvoiceException() {
            super("Fee invoice not found");
        }
    }
}
