package com.scube.record.features.document.service;

import com.scube.record.features.document.dto.CreateDocumentRequest;
import com.scube.record.features.document.dto.DocumentDto;
import com.scube.record.features.document.dto.UpdateDocumentRequest;
import com.scube.record.infrastructure.db.entity.document.Document;
import com.scube.record.infrastructure.db.entity.document.DocumentType;
import com.scube.record.infrastructure.db.repository.document.DocumentRepository;
import com.scube.record.infrastructure.db.repository.document.DocumentTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class DocumentService {

    private final DocumentRepository documentRepository;
    private final DocumentTypeRepository documentTypeRepository;

    public DocumentDto createDocument(CreateDocumentRequest request, String createdBy) {
        DocumentType documentType = documentTypeRepository.findActiveByTypeKey(request.getDocumentTypeKey())
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document type not found with key: " + request.getDocumentTypeKey()));

        Document document = new Document();
        document.setFileName(request.getFileName());
        document.setContentType(request.getContentType());
        document.setFileSize(request.getFileSize());
        document.setFileUrl(request.getFileUrl());
        document.setDocumentServiceUuid(request.getDocumentServiceUuid());
        document.setDocumentType(documentType);
        document.setProperties(request.getProperties());
        document.setCreatedBy(createdBy);
        document.setLastModifiedBy(createdBy);

        Document savedDocument = documentRepository.save(document);
        return convertToDto(savedDocument);
    }

    public DocumentDto updateDocument(UUID documentUuid, UpdateDocumentRequest request, String modifiedBy) {
        Document document = documentRepository.findActiveByDocumentUuid(documentUuid)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document not found with UUID: " + documentUuid));

        if (StringUtils.hasText(request.getFileName())) {
            document.setFileName(request.getFileName());
        }
        if (StringUtils.hasText(request.getContentType())) {
            document.setContentType(request.getContentType());
        }
        if (request.getFileSize() != null) {
            document.setFileSize(request.getFileSize());
        }
        if (StringUtils.hasText(request.getFileUrl())) {
            document.setFileUrl(request.getFileUrl());
        }
        if (StringUtils.hasText(request.getDocumentTypeKey())) {
            DocumentType documentType = documentTypeRepository.findActiveByTypeKey(request.getDocumentTypeKey())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                    "Document type not found with key: " + request.getDocumentTypeKey()));
            document.setDocumentType(documentType);
        }
        if (request.getProperties() != null) {
            document.setProperties(request.getProperties());
        }
        document.setLastModifiedBy(modifiedBy);

        Document updatedDocument = documentRepository.save(document);
        return convertToDto(updatedDocument);
    }

    @Transactional(readOnly = true)
    public Optional<DocumentDto> findByDocumentUuid(UUID documentUuid) {
        return documentRepository.findActiveByDocumentUuid(documentUuid)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public Page<DocumentDto> findAllDocuments(Pageable pageable) {
        Page<Document> documents = documentRepository.findAll(
            (Specification<Document>) (root, query, criteriaBuilder) ->
                criteriaBuilder.isFalse(root.get("isDeleted")),
            pageable);

        List<DocumentDto> documentDtos = documents.getContent().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        return new PageImpl<>(documentDtos, pageable, documents.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<DocumentDto> findDocumentsByCreatedBy(String createdBy, Pageable pageable) {
        List<Document> documents = documentRepository.findAllActiveByCreatedBy(createdBy);
        List<DocumentDto> documentDtos = documents.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), documentDtos.size());

        return new PageImpl<>(
            documentDtos.subList(start, end),
            pageable,
            documentDtos.size()
        );
    }

    @Transactional(readOnly = true)
    public Page<DocumentDto> findDocumentsByDocumentType(String documentTypeKey, Pageable pageable) {
        DocumentType documentType = documentTypeRepository.findActiveByTypeKey(documentTypeKey)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document type not found with key: " + documentTypeKey));

        List<Document> documents = documentRepository.findAllActiveByDocumentType(documentType.getDocumentTypeId());
        List<DocumentDto> documentDtos = documents.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), documentDtos.size());

        return new PageImpl<>(
            documentDtos.subList(start, end),
            pageable,
            documentDtos.size()
        );
    }

    public void softDeleteDocument(UUID documentUuid, String deletedBy) {
        Document document = documentRepository.findActiveByDocumentUuid(documentUuid)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document not found with UUID: " + documentUuid));

        document.setDeleted(true);
        document.setDeletedDate(Instant.now());
        document.setLastModifiedBy(deletedBy);

        documentRepository.save(document);
    }

    public void hardDeleteDocument(UUID documentUuid) {
        Document document = documentRepository.findByDocumentUuid(documentUuid)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document not found with UUID: " + documentUuid));

        documentRepository.delete(document);
    }

    private DocumentDto convertToDto(Document document) {
        DocumentDto dto = new DocumentDto();
        dto.setDocumentId(document.getDocumentId());
        dto.setDocumentUuid(document.getDocumentUuid());
        dto.setCreatedAt(document.getCreatedAt());
        dto.setCreatedBy(document.getCreatedBy());
        dto.setLastModifiedAt(document.getLastModifiedAt());
        dto.setLastModifiedBy(document.getLastModifiedBy());
        dto.setProperties(document.getProperties());
        dto.setEvents(document.getEvents());
        dto.setContentType(document.getContentType());
        dto.setFileName(document.getFileName());
        dto.setFileSize(document.getFileSize());
        dto.setFileUrl(document.getFileUrl());
        dto.setDocumentServiceUuid(document.getDocumentServiceUuid());
        dto.setDeletedDate(document.getDeletedDate());
        dto.setDeleted(document.isDeleted());

        if (document.getDocumentType() != null) {
            dto.setDocumentTypeId(document.getDocumentType().getDocumentTypeId());
            dto.setDocumentTypeKey(document.getDocumentType().getTypeKey());
            dto.setDocumentTypeName(document.getDocumentType().getName());
        }

        return dto;
    }
}