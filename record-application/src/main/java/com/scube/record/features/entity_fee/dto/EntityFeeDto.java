package com.scube.record.features.entity_fee.dto;

import com.scube.record.infrastructure.db.entity.entity_fee.EntityFee;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityFeeDto {

    private Long entityFeeId;
    private String feeName;
    private BigDecimal feeAmount;
    private BigDecimal paidAmount;
    private BigDecimal outstandingAmount;
    private EntityFee.PaymentStatus paymentStatus;
    private Instant dueDate;
    private Instant paidDate;
    private String orderId;
    private Boolean isRecurring;
    private String recurringFrequency;
    private Instant nextDueDate;
    private Instant lastProcessedDate;
    private Map<String, Object> customFields;
    private Map<String, Object> properties;

    // Audit fields
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private Boolean isDeleted;
}