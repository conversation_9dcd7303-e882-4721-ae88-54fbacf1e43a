package com.scube.record.features.record.fee;

import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * RabbitMQ query to get fees from Calculation Service
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetFeesQuery implements IRabbitFanoutPublisherRpc<GetFeesQueryResponse> {
    private List<String> feeCodes;
}
