package com.scube.record.features.module.service;

import com.scube.record.features.module.dto.CreateModuleRequest;
import com.scube.record.features.module.dto.ModuleResponse;
import com.scube.record.features.module.dto.UpdateModuleRequest;
import com.scube.record.features.module.exception.DuplicateModuleException;
import com.scube.record.features.module.exception.ModuleNotFoundException;
import com.scube.record.features.module.mapper.ModuleMapper;
import com.scube.record.infrastructure.db.entity.module.ModuleDefinition;
import com.scube.record.infrastructure.db.repository.module.ModuleRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing modules.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ModuleService {

    private final ModuleRepository moduleRepository;
    private final RecordTypeRepository recordTypeRepository;
    private final ModuleSyncService moduleSyncService;
    private final ModuleMapper moduleMapper;

    /**
     * Create a new module.
     */
    public ModuleResponse createModule(CreateModuleRequest request) {
        log.info("Creating new module: {}", request.getModuleCode());

        // Check if module already exists
        if (moduleRepository.existsByModuleCode(request.getModuleCode())) {
            throw new DuplicateModuleException(
                    "Module already exists with code: " + request.getModuleCode());
        }

        // Create module entity
        ModuleDefinition module = moduleMapper.toEntity(request);

        // Save module
        module = moduleRepository.save(module);
        log.info("Module saved with ID: {}", module.getModuleId());

        // Sync to database (create record types, etc.)
        try {
            moduleSyncService.syncModuleToDatabase(module);
            log.info("Module synced to database successfully");
        } catch (Exception e) {
            log.error("Failed to sync module to database", e);
            // Rollback: delete the module
            moduleRepository.delete(module);
            throw new RuntimeException("Failed to sync module: " + e.getMessage(), e);
        }

        // Get record type count
        Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());

        return moduleMapper.toResponse(module, recordTypeCount);
    }

    /**
     * Update an existing module.
     */
    public ModuleResponse updateModule(UUID moduleUuid, UpdateModuleRequest request) {
        log.info("Updating module: {}", moduleUuid);

        ModuleDefinition module = moduleRepository.findByModuleUuid(moduleUuid)
                .orElseThrow(() -> new ModuleNotFoundException(
                        "Module not found with UUID: " + moduleUuid));

        // Update fields
        if (request.getModuleName() != null) {
            module.setModuleName(request.getModuleName());
        }
        if (request.getDescription() != null) {
            module.setDescription(request.getDescription());
        }
        if (request.getVersion() != null) {
            module.setVersion(request.getVersion());
        }
        if (request.getConfig() != null) {
            module.setConfig(request.getConfig());
            // Re-sync if config changed
            moduleSyncService.syncModuleToDatabase(module);
        }
        if (request.getIsActive() != null) {
            module.setIsActive(request.getIsActive());
        }

        module.setLastModifiedBy("api");

        module = moduleRepository.save(module);
        log.info("Module updated successfully");

        Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
        return moduleMapper.toResponse(module, recordTypeCount);
    }

    /**
     * Get module by UUID.
     */
    @Transactional(readOnly = true)
    public ModuleResponse getModuleByUuid(UUID moduleUuid) {
        ModuleDefinition module = moduleRepository.findByModuleUuid(moduleUuid)
                .orElseThrow(() -> new ModuleNotFoundException(
                        "Module not found with UUID: " + moduleUuid));

        Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
        return moduleMapper.toResponse(module, recordTypeCount);
    }

    /**
     * Get module by code.
     */
    @Transactional(readOnly = true)
    public ModuleResponse getModuleByCode(String moduleCode) {
        ModuleDefinition module = moduleRepository.findByModuleCode(moduleCode)
                .orElseThrow(() -> new ModuleNotFoundException(
                        "Module not found with code: " + moduleCode));

        Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
        return moduleMapper.toResponse(module, recordTypeCount);
    }

    /**
     * Get all modules with pagination.
     */
    @Transactional(readOnly = true)
    public Page<ModuleResponse> getAllModules(Pageable pageable) {
        Page<ModuleDefinition> modulePage = moduleRepository.findAll(pageable);

        List<ModuleResponse> responses = modulePage.getContent().stream()
                .map(module -> {
                    Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
                    return moduleMapper.toResponse(module, recordTypeCount);
                })
                .collect(Collectors.toList());

        return new PageImpl<>(responses, pageable, modulePage.getTotalElements());
    }

    /**
     * Get active modules.
     */
    @Transactional(readOnly = true)
    public Page<ModuleResponse> getActiveModules(Pageable pageable) {
        Page<ModuleDefinition> modulePage = moduleRepository.findByIsActive(true, pageable);

        List<ModuleResponse> responses = modulePage.getContent().stream()
                .map(module -> {
                    Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
                    return moduleMapper.toResponse(module, recordTypeCount);
                })
                .collect(Collectors.toList());

        return new PageImpl<>(responses, pageable, modulePage.getTotalElements());
    }

    /**
     * Search modules by keyword.
     */
    @Transactional(readOnly = true)
    public Page<ModuleResponse> searchModules(String keyword, Pageable pageable) {
        Page<ModuleDefinition> modulePage = moduleRepository.searchByKeyword(keyword, pageable);

        List<ModuleResponse> responses = modulePage.getContent().stream()
                .map(module -> {
                    Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
                    return moduleMapper.toResponse(module, recordTypeCount);
                })
                .collect(Collectors.toList());

        return new PageImpl<>(responses, pageable, modulePage.getTotalElements());
    }

    /**
     * Delete a module.
     */
    public void deleteModule(UUID moduleUuid) {
        log.info("Deleting module: {}", moduleUuid);

        ModuleDefinition module = moduleRepository.findByModuleUuid(moduleUuid)
                .orElseThrow(() -> new ModuleNotFoundException(
                        "Module not found with UUID: " + moduleUuid));

        // Delete all record types created by this module
        moduleSyncService.deleteModuleData(module.getModuleCode());

        // Delete the module definition
        moduleRepository.delete(module);

        log.info("Module deleted successfully");
    }

    /**
     * Re-sync a module (update record types with latest config).
     */
    public ModuleResponse resyncModule(UUID moduleUuid) {
        log.info("Re-syncing module: {}", moduleUuid);

        ModuleDefinition module = moduleRepository.findByModuleUuid(moduleUuid)
                .orElseThrow(() -> new ModuleNotFoundException(
                        "Module not found with UUID: " + moduleUuid));

        moduleSyncService.syncModuleToDatabase(module);

        Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
        return moduleMapper.toResponse(module, recordTypeCount);
    }

    /**
     * Activate/deactivate a module.
     */
    public ModuleResponse toggleModuleStatus(UUID moduleUuid, boolean isActive) {
        log.info("Setting module {} status to: {}", moduleUuid, isActive);

        ModuleDefinition module = moduleRepository.findByModuleUuid(moduleUuid)
                .orElseThrow(() -> new ModuleNotFoundException(
                        "Module not found with UUID: " + moduleUuid));

        module.setIsActive(isActive);
        module.setLastModifiedBy("api");
        module = moduleRepository.save(module);

        Long recordTypeCount = recordTypeRepository.countByModuleCode(module.getModuleCode());
        return moduleMapper.toResponse(module, recordTypeCount);
    }
}

