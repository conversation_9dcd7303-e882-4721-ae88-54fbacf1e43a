package com.scube.record.features.record.fee;

import com.scube.calculation.dto.gen_dto.CartInvoiceResponse;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * RabbitMQ query to get cart invoice from Calculation Service
 * Used for record fee preview and cart management
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetCartItemInvoiceQuery implements IRabbitFanoutPublisherRpc<CartInvoiceResponse> {
    private List<AddItemRequest> items;

    public GetCartItemInvoiceQuery(String... fees) {
        items = new ArrayList<>();
        var item = new AddItemRequest();
        for (String fee : fees) {
            item.getFees().add(fee);
        }
        this.items.add(item);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddItemRequest implements Serializable {
        private String itemType;
        private UUID itemId;
        private BigDecimal basePrice;
        private String name;
        private String description;
        private Map<String, Object> properties = new HashMap<>();
        private List<String> fees = new ArrayList<>();
    }
}
