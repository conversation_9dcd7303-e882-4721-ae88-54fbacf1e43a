package com.scube.record.features.record.fee;

import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * Listens to RefundedEvent from Payment Service
 * Handles record fee refunds
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RefundedEventHandler extends FanoutListener<RefundedEventHandler.RefundedEvent> {

    @Override
    public void consume(RefundedEvent event) {
        log.info("Processing RefundedEvent for orderId: {}, amount: {}", 
                event.orderId(), event.refundedAmount());
        
        // TODO: Implement record-specific refund logic
        // This is where you would:
        // 1. Mark record fees as refunded
        // 2. Update record status if needed
        // 3. Trigger any post-refund workflows
        
        log.info("Refund processed for order: {}", event.orderId());
    }

    public record RefundedEvent(UUID orderId, BigDecimal refundedAmount) implements IRabbitFanoutSubscriber {
    }
}
