package com.scube.record.infrastructure.db.entity.record;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "association_type")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class AssociationType {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "association_type_id")
    private Long associationTypeId;

    @Column(name = "association_type_uuid", length = 36)
    private String associationTypeUuid;

    @Column(name = "association_name", length = 255, nullable = false)
    private String associationName;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private JsonNode properties;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "created_by", length = 100, nullable = false)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedAt;

    @Column(name = "last_modified_by", length = 100, nullable = false)
    private String lastModifiedBy;

    @PrePersist
    private void generateUuid() {
        if (associationTypeUuid == null) {
            associationTypeUuid = UUID.randomUUID().toString();
        }
    }
}