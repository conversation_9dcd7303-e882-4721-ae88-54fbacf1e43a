package com.scube.record.infrastructure.db.entity.module;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.UUID;

/**
 * Entity representing a module definition.
 * Stores the complete module configuration as JSON.
 * Modules define record types, associations, fee rules, and other configurations.
 */
@Entity
@Table(name = "module_definition")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModuleDefinition {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "module_id")
    private Long moduleId;

    @Column(name = "module_uuid", unique = true, nullable = false)
    private UUID moduleUuid;

    @Column(name = "module_code", unique = true, nullable = false, length = 100)
    private String moduleCode;

    @Column(name = "module_name", nullable = false)
    private String moduleName;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "version", length = 50)
    private String version;

    /**
     * Complete module configuration stored as JSON.
     * Contains recordTypes, associationTypes, documentTypes, permissions, settings, etc.
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "config", columnDefinition = "jsonb", nullable = false)
    private JsonNode config;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "created_by", length = 100, nullable = false)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Instant lastModifiedAt;

    @Column(name = "last_modified_by", length = 100, nullable = false)
    private String lastModifiedBy;

    @PrePersist
    private void generateUuid() {
        if (moduleUuid == null) {
            moduleUuid = UUID.randomUUID();
        }
        if (createdBy == null) {
            createdBy = "system";
        }
        if (lastModifiedBy == null) {
            lastModifiedBy = "system";
        }
    }

    @PreUpdate
    private void updateModifiedBy() {
        if (lastModifiedBy == null) {
            lastModifiedBy = "system";
        }
    }
}

