FROM eclipse-temurin:21-jre-alpine

# Set the working directory
WORKDIR /app

# Copy the application JAR file
COPY record-application/target/record-application-*.jar app.jar

# Expose the application port
EXPOSE 9011

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget -q --spider http://localhost:9011/api/record/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "/app.jar"]